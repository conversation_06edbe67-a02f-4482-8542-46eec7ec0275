# -*- coding: utf-8 -*-
import json
from typing import Di<PERSON>, <PERSON><PERSON>, List
from db_utils import DatabaseClient
from core.validation_engine import ValidationEngine


class Validator:
    """Main validator class that orchestrates worksheet validation using the new ValidationEngine"""

    def __init__(self, db_client: DatabaseClient = None):
        self.config_data = None
        self.db_client = db_client or DatabaseClient()
        self.validation_engine = ValidationEngine()

    def validate_data(self, data: dict, config: Dict) -> Tuple[bool, dict]:
        """Validate worksheet data against configuration rules"""
        config_data = self._extract_validation_config(config)
        if not config_data:
            return False, {"error": "Configuration not found"}

        all_valid = True

        # Initialize group-level exceptions at the top level
        if "group_top_exceptions" not in data:
            data["group_top_exceptions"] = {}

        # First, handle group-level validations (where groups: [] is empty)
        group_level_rules = self._find_validation_rules(config_data, is_group_level=True)
        for rule in group_level_rules:
            # For group-level validations, we validate against the entire data structure
            field_config = {
                "validation_rules": [rule]
            }
            rule_errors = self.validation_engine.validate_field(
                data, field_config, config_data, data, None, None
            )
            if rule_errors:
                all_valid = False
                rule_id = rule.get("id", "unknown_rule")
                data["group_top_exceptions"][rule_id] = "; ".join(rule_errors)

        # Then handle field-level validations
        groups = data.get("groups", {})

        for group_name, group_data in groups.items():
            if "bre_exceptions" not in data["groups"][group_name]:
                data["groups"][group_name]["bre_exceptions"] = {}

            fields = group_data.get("fields", {})

            for field_name, field_data in fields.items():
                field_value = field_data.get("value") if isinstance(field_data, dict) else field_data

                # Get field-specific rules using the same function
                field_rules = self._find_validation_rules(
                    config_data, 
                    is_group_level=False, 
                    group_name=group_name, 
                    field_name=field_name
                )
                
                if not field_rules:
                    continue

                field_config = {
                    "validation_rules": field_rules
                }

                field_errors = self.validation_engine.validate_field(
                    field_value, field_config, config_data, data, group_name, field_name
                )

                if field_errors:
                    all_valid = False
                    data["groups"][group_name]["bre_exceptions"][field_name] = "; ".join(field_errors)

        return all_valid, data

    def _extract_validation_config(self, config: Dict) -> list:
        """Extract validation configuration from config"""
        # The new structure has 'properties' as a list of validation rule groups
        if 'properties' in config:
            return config['properties']
        # Fallback for backward compatibility - return empty list if no properties
        return []

    def _find_validation_rules(self, config_data: list, is_group_level: bool = False, 
                             group_name: str = None, field_name: str = None) -> List[Dict]:
        """
        Find validation rules based on the criteria:
        - If is_group_level=True: Find rules where groups is empty or not present
        - If is_group_level=False: Find rules for specific group.field.value pattern
        """
        found_rules = []

        for property_group in config_data:
            validation_rules = property_group.get("validation_rules", [])
            for rule in validation_rules:
                
                if is_group_level:
                    # Check if groups is empty or not present (indicating top-level validation)
                    groups = rule.get("groups", [])
                    if not groups:  # Empty list means it's a group-level validation
                        converted_rule = self._convert_rule_format(rule)
                        converted_rule["id"] = rule.get("id", "")  # Keep the id for group-level rules
                        found_rules.append(converted_rule)
                
                else:
                    # Field-level validation: look for exact match of group.field.value pattern
                    rule_id = rule.get("id", "")
                    target_pattern = f"{group_name}.{field_name}.value"
                    
                    if rule_id == target_pattern:
                        converted_rule = self._convert_rule_format(rule)
                        found_rules.append(converted_rule)

        return found_rules

    def _convert_rule_format(self, rule: Dict) -> Dict:
        """Convert rule from config format to validation engine format"""
        return {
            "isValidationType": rule.get("ValidationType", ""),
            "regexes": rule.get("regexes", []),
            "expressions": rule.get("expressions", []),
            "error_msgs": rule.get("error_msgs", []),
            "conditionType": rule.get("conditionType", "AND")
        }


def example_worksheet_usage():
    """Example usage of the WorksheetValidator"""
    # Load worksheet data
    with open("test_workSheet.json", "r") as f:
        worksheet_data = json.load(f)

    client = DatabaseClient()
    validator = Validator(client)

    # Load config from database
    key = "tag_titles"
    config_data = client.get_config(key)
    if not config_data:
        raise ValueError(f"Configuration not found in database for key: {key}")

    print(f"✅ Configuration loaded from database for key: {key}")
    print(f"📊 Config structure: {list(config_data.keys())}")

    is_valid, updated_data = validator.validate_data(worksheet_data, config_data)

    if is_valid:
        print("✅ Worksheet validation passed!")
    else:
        print("❌ Worksheet validation failed. Check bre_exceptions in groups:")

        total_exceptions = 0

        # Display group-level exceptions at the top level
        group_top_exceptions = updated_data.get("group_top_exceptions", {})
        if group_top_exceptions:
            print(f"\n  🔍 Group-level exceptions ({len(group_top_exceptions)}):")
            for rule_name, error in group_top_exceptions.items():
                print(f"    ❌ {rule_name}: {error}")
            total_exceptions += len(group_top_exceptions)

        for group_name, group_data in updated_data.get("groups", {}).items():
            exceptions = group_data.get("bre_exceptions", {})

            if exceptions:
                total_group_exceptions = len(exceptions)
                total_exceptions += total_group_exceptions
                print(f"\n  📋 Group '{group_name}' has {total_group_exceptions} exception(s):")

                # Display field-level exceptions
                for field, error in exceptions.items():
                    print(f"    ❌ {field}: {error}")
            else:
                print(f"\n  ✅ Group '{group_name}': No exceptions")

        if total_exceptions == 0:
            print("\n  ℹ️  No specific field exceptions found, but validation failed.")
            print("     This might indicate a configuration or data structure issue.")

    with open("validated_output.json", "w") as f:
        json.dump(updated_data, f, indent=2)
    print(f"\n💾 Updated data with bre_exceptions saved to 'validated_output.json'")

    return updated_data


if __name__ == "__main__":
    example_worksheet_usage()