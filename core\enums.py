"""
Enumeration classes for validation system
"""

from enum import Enum


class ValidationType(Enum):
    """Enumeration for validation types"""
    REGEX_LIST = "REGEX_LIST"
    EXPRESSION_TYPE_LIST = "EXPRESSION_TYPE_LIST"


class ValidationResult(Enum):
    """Enumeration for validation results"""
    VALID = "VALID"
    INVALID = "INVALID"
    ERROR = "ERROR"


class ConditionType(Enum):
    """Enumeration for condition types in multi-rule validation"""
    AND = "AND"
    OR = "OR"

